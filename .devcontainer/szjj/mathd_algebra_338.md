# MATHD Algebra 338 Proof Tree

## Problem Statement
Solve the linear system:
- 3a + b + c = -3
- a + 3b + c = 9
- a + b + 3c = 19

Show that the product abc equals -56.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that for the unique solution (a, b, c) of the linear system, abc = -56
**Strategy**: Solve the system by pairwise equation subtraction to find linear relations, then substitute back to determine each variable

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Find linear relations by subtracting equations pairwise
2. Substitute the relations into one original equation to solve for one variable
3. Use the linear relations to compute the other variables
4. Calculate the product abc and verify it equals -56
**Strategy**: Direct algebraic manipulation using equation subtraction and substitution

### SUBGOAL_001 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Find linear relation between a and b: b = a + 6
**Strategy**: Subtract equation (1) from equation (2): (a + 3b + c) - (3a + b + c) = 9 - (-3)
**Status**: [PROVEN]
**Proof Completion**: Successfully implemented using linear_combination tactic to subtract equations and linarith to derive the linear relation b = a + 6.

### SUBGOAL_002 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Find linear relation between b and c: c = b + 5
**Strategy**: Subtract equation (2) from equation (3): (a + b + 3c) - (a + 3b + c) = 19 - 9
**Status**: [PROVEN]
**Proof Completion**: Successfully implemented using linear_combination tactic to subtract equations and linarith to derive the linear relation c = b + 5.

### SUBGOAL_003 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Solve for a by substituting relations into equation (1): a = -4
**Strategy**: Substitute b = a + 6 and c = b + 5 = a + 11 into 3a + b + c = -3
**Status**: [PROVEN]
**Proof Completion**: Successfully implemented using calc to substitute the linear relations and linarith to solve for a = -4.

### SUBGOAL_004 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Compute b using the relation: b = 2
**Strategy**: Use b = a + 6 with a = -4
**Status**: [PROVEN]
**Proof Completion**: Successfully implemented using substitution and linarith for arithmetic computation.

### SUBGOAL_005 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Compute c using the relation: c = 7
**Strategy**: Use c = b + 5 with b = 2
**Status**: [PROVEN]
**Proof Completion**: Successfully implemented using substitution and linarith for arithmetic computation.

### SUBGOAL_006 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Calculate the product: abc = -56
**Strategy**: Compute (-4) * 2 * 7 = -56
**Status**: [PROVEN]
**Proof Completion**: Successfully implemented using substitution and norm_num for arithmetic computation.

### SUBGOAL_007 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Verify the solution satisfies all three original equations
**Strategy**: Check that a = -4, b = 2, c = 7 satisfies each equation in the system
**Status**: [PROVEN]
**Proof Completion**: Successfully implemented using substitution and norm_num to verify all three equations.

### SUBGOAL_008 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Establish the final conclusion: abc = -56 for the unique solution
**Strategy**: Combine all previous steps into a complete proof
**Status**: [PROVEN]
**Proof Completion**: Successfully completed by combining all previous steps. The final proof demonstrates that the unique solution a = -4, b = 2, c = 7 satisfies all equations and yields abc = -56.
