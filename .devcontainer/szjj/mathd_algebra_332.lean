-- Problem: Given x + y = 14 and xy = 19, compute x² + y².

import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Ring.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.NormNum

-- Main theorem: Given the constraints, x² + y² = 158
theorem mathd_algebra_332 (x y : ℝ) (h1 : x + y = 14) (h2 : x * y = 19) :
  x^2 + y^2 = 158 := by
  -- Step 1: Establish the identity x² + y² = (x + y)² - 2xy
  have identity : x^2 + y^2 = (x + y)^2 - 2 * (x * y) := by
    ring

  -- Step 2: Substitute the given values
  have substitution : (x + y)^2 - 2 * (x * y) = 14^2 - 2 * 19 := by
    rw [h1, h2]

  -- Step 3: Compute the numerical result
  have computation : 14^2 - 2 * 19 = 158 := by
    norm_num

  -- Combine all steps
  rw [identity, substitution]
  norm_cast
