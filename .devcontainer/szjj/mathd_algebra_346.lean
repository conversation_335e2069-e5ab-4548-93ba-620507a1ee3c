import Mathlib.Data.Real.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum

-- MathD Algebra 346: Function Composition Evaluation
-- Problem: Compute g(f(5) - 1) for f(x) = 2x - 3 and g(x) = x + 1

-- Define the functions f and g
def f (x : ℝ) : ℝ := 2 * x - 3
def g (x : ℝ) : ℝ := x + 1

-- Main theorem: g(f(5) - 1) = 7
theorem mathd_algebra_346 : g (f 5 - 1) = 7 := by
  -- Direct computation approach
  unfold f g
  norm_num

-- Helper lemma: Evaluate f(5)
lemma f_at_5 : f 5 = 7 := by
  unfold f
  norm_num

-- Helper lemma: Compute f(5) - 1
lemma f_5_minus_1 : f 5 - 1 = 6 := by
  rw [f_at_5]
  norm_num

-- Helper lemma: Evaluate g(6)
lemma g_at_6 : g 6 = 7 := by
  unfold g
  norm_num

-- Helper lemma: Function definition verification for f
lemma f_def_check (x : ℝ) : f x = 2 * x - 3 := by
  unfold f
  rfl

-- Helper lemma: Function definition verification for g
lemma g_def_check (x : ℝ) : g x = x + 1 := by
  unfold g
  rfl

-- Alternative direct computation approach
lemma direct_computation : g (f 5 - 1) = g (2 * 5 - 3 - 1) := by
  unfold f
  norm_num

-- Arithmetic verification lemma
lemma arithmetic_steps : 2 * 5 = 10 ∧ 10 - 3 = 7 ∧ 7 - 1 = 6 ∧ 6 + 1 = 7 := by
  norm_num
