import Mathlib.Data.Rat.Defs
import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum
import Mathlib.Tactic.FieldSimp
import Mathlib.Tactic.Linarith

-- MathD Algebra 342: Find first term of arithmetic sequence with S₅ = 70 and S₁₀ = 210
theorem mathd_algebra_342 :
  ∃ (a d : ℚ),
    -- S₅ = 70: sum of first 5 terms
    (5 : ℚ) / 2 * (2 * a + 4 * d) = 70 ∧
    -- S₁₀ = 210: sum of first 10 terms
    (10 : ℚ) / 2 * (2 * a + 9 * d) = 210 ∧
    -- The first term is 42/5
    a = 42 / 5 := by

  -- Use a = 42/5 and d = 14/5
  use 42/5, 14/5

  constructor
  -- Prove S₅ = 70
  · -- S₅ = 5/2 * (2a + 4d) = 70
    -- Substitute a = 42/5, d = 14/5
    have h_s5_formula : (5 : ℚ) / 2 * (2 * (42/5) + 4 * (14/5)) = 70 := by
      -- Simplify: 5/2 * (84/5 + 56/5) = 5/2 * (140/5) = 5/2 * 28 = 70
      norm_num
    exact h_s5_formula

  constructor
  -- Prove S₁₀ = 210
  · -- S₁₀ = 10/2 * (2a + 9d) = 210
    -- Substitute a = 42/5, d = 14/5
    have h_s10_formula : (10 : ℚ) / 2 * (2 * (42/5) + 9 * (14/5)) = 210 := by
      -- Simplify: 5 * (84/5 + 126/5) = 5 * (210/5) = 5 * 42 = 210
      norm_num
    exact h_s10_formula

  -- Prove a = 42/5
  · rfl
